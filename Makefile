# Proto parameters
PROTOC=protoc
PROTO_DIR=api
PROTO_OUT_DIR=.
MODULE_NAME=github.com/xuezhaojun/multiclustertunnel

# Find all proto files under api/
PROTO_FILES=$(shell find $(PROTO_DIR) -name "*.proto")

# Install required tools for proto generation
.PHONY: install-tools
install-tools:
	@echo "Installing protoc plugins..."
	go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.3.0
	@echo "Tools installed successfully!"

# Generate Go code from proto files
.PHONY: proto-gen
proto-gen: install-tools
	@echo "Generating Go code from proto files..."
	$(PROTOC) \
		--go_out=$(PROTO_OUT_DIR) \
		--go_opt=module=$(MODULE_NAME) \
		--go-grpc_out=$(PROTO_OUT_DIR) \
		--go-grpc_opt=module=$(MODULE_NAME) \
		--proto_path=$(PROTO_DIR) \
		$(PROTO_FILES)
	@echo "Proto generation completed!"

# Clean generated proto files
.PHONY: proto-clean
proto-clean:
	@echo "Cleaning generated proto files..."
	@find $(PROTO_OUT_DIR) -name "*.pb.go" -delete
	@find $(PROTO_OUT_DIR) -name "*_grpc.pb.go" -delete
	@echo "Clean completed!"

# Test parameters
TEST_TIMEOUT=10m
TEST_PACKAGES=./...

# Run unit tests (excluding performance tests)
.PHONY: test
test:
	@echo "Running unit tests..."
	go test -short -v -timeout $(TEST_TIMEOUT) $(TEST_PACKAGES)
	@echo "Unit tests completed!"

# Run all tests including performance tests
.PHONY: test-all
test-all:
	@echo "Running all tests (including performance tests)..."
	go test -v -timeout $(TEST_TIMEOUT) $(TEST_PACKAGES)
	@echo "All tests completed!"

# Run performance tests only
.PHONY: test-perf
test-perf:
	@echo "Running performance tests..."
	go test -v -timeout $(TEST_TIMEOUT) -run "Performance|Concurrent.*Limits" $(TEST_PACKAGES)
	@echo "Performance tests completed!"

# Run benchmarks
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	go test -bench=. -benchmem -timeout $(TEST_TIMEOUT) $(TEST_PACKAGES)
	@echo "Benchmarks completed!"

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -short -v -timeout $(TEST_TIMEOUT) -coverprofile=coverage.out $(TEST_PACKAGES)
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run tests with race detection
.PHONY: test-race
test-race:
	@echo "Running tests with race detection..."
	go test -short -v -timeout $(TEST_TIMEOUT) -race $(TEST_PACKAGES)
	@echo "Race detection tests completed!"

# Clean test artifacts
.PHONY: test-clean
test-clean:
	@echo "Cleaning test artifacts..."
	@rm -f coverage.out coverage.html
	@echo "Test artifacts cleaned!"